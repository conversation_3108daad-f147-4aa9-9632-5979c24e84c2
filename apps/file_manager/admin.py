from django.contrib import admin
from django.template.defaultfilters import filesizeformat
from common.config import BaseAdmin

from apps.file_manager.models.file_upload import FileUpload


# --------------------------------------------------------
# FILE UPLOAD ADMIN CONFIGURATION
# --------------------------------------------------------

@admin.register(FileUpload)
class FileUploadAdmin(BaseAdmin):
    """
    Admin configuration for the FileUpload model.
    """
    list_display = (
        "user",
        "original_name",
        "file_type",
        "format_file_size",
        "created_at",
    )
    list_filter = ("file_type", "created_at")
    search_fields = ("user__email", "original_name", "file_type")
    readonly_fields = ("size", "file_type", "original_name")
    ordering = ("-created_at",)
    date_hierarchy = "created_at"

    def format_file_size(self, obj):
        """
        Return the formatted file size for display in the admin.
        """
        if obj.size is not None:
            return filesizeformat(obj.size)
        return "Unknown size"

    # Add metadata for `format_file_size` to improve clarity
    format_file_size.short_description = "File Size"  # type: ignore
    format_file_size.admin_order_field = "size"  # type: ignore
