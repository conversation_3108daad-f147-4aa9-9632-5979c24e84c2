from typing import Any, Dict
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.paginations import CustomPagination
from dxh_libraries.rest_framework import Response, status, MultiPartParser, FormParser
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView

from apps.file_manager.api.v1.serializers import FileSerializer
from apps.file_manager.services import FileService
from apps.llm_manager.utils.file_types import SUPPORTED_FILE_TYPES, is_supported_file_type
from apps.user.services.daily_message_usage_service import DailyMessageUsageService

logger = Logger(__name__)


class FileListView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            files = self.file_service.get_files(request.user)
            paginated_data = self.paginator.paginate_queryset(files, request)
            serializer = FileSerializer(paginated_data, many=True, context={"request": request})
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Files retrieved successfully"),
                "data": records,
                "pagination": pagination
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "FileListView:get",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class FileDetailView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()

    def get(self, request, file_id):
        try:
            file = self.file_service.get(id=file_id)
            if not file:
                result = {
                    "message": _("File not found"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            serializer = FileSerializer(file, context={"request": request})
            result = {
                "message": _("File retrieved successfully"),
                "data": serializer.data,
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "FileDetailView:get",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def delete(self, request, file_id):
        try:
            self.file_service.delete_file(file_id, request.user)
            result = {
                "message": _("File deleted successfully"),
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "FileDetailView:delete",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class FileDownloadView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()

    def get(self, request, file_id):
        try:
            file_response = self.file_service.download_file(file_id)

            return file_response
        
        except Exception as e:
            logger.error({"event": "FileDownloadView:get",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class BatchFileDeleteView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()

    def post(self, request):
        try:
            file_ids = request.data.get("file_ids", [])
            if not file_ids:
                result = {
                    "message": _("No files specified"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            self.file_service.batch_delete_files(file_ids, request.user)
            result = {
                "message": _("Files deleted successfully"),
            }

            return Response(result, status=status.HTTP_204_NO_CONTENT)

        except Exception as e:
            logger.error({"event": "BatchFileDeleteView:post",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class BatchFileDownloadView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()

    def post(self, request):
        try:
            file_ids = request.data.get("file_ids", [])
            if not file_ids:
                result = {
                    "message": _("No files specified"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            return self.file_service.batch_download_files(file_ids, request.user)

        except Exception as e:
            logger.error({"event": "BatchFileDownloadView:post",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class FileSearchView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()
        self.paginator = CustomPagination()

    def get(self, request):
        try:
            query = request.query_params.get("q", "")
            file_type = request.query_params.get("type")

            files = self.file_service.search_files(request.user, query, file_type)
            paginated_data = self.paginator.paginate_queryset(files, request)
            serializer = FileSerializer(paginated_data, many=True, context={"request": request})
            paginated_response = self.paginator.get_paginated_response(serializer.data)

            paginated_response_data = paginated_response.data
            records = paginated_response_data["records"]
            pagination = paginated_response_data["pagination"]

            result = {
                "message": _("Files retrieved successfully"),
                "data": records,
                "pagination": pagination
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "FileSearchView:get",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e


class FileUploadView(BaseApiView):
    parser_classes = [MultiPartParser, FormParser]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.file_service = FileService()
        self.daily_usage_service = DailyMessageUsageService()

    def post(self, request):
        try:
            payload = request.data
            file = payload.get("file")
            if request.user.subscription_type == "free":
                result = {
                    "message": _("File upload is only available for premium users")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)
            
            daily_limit = self.daily_usage_service._get_user_daily_file_upload_limit(request.user)
            current_count = self.daily_usage_service.get_daily_file_useage(request.user)
            
            if current_count >= daily_limit:
                result = {
                    "message": _(f"You've reached your daily file upload limit ({current_count}/{daily_limit})")
                }
                return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)
            
            file_extension = file.name.split(".")[-1].lower()
            if not is_supported_file_type(file_extension):
                result = {
                    "message": _(f"Unsupported file type. Supported types: {', '.join(SUPPORTED_FILE_TYPES.keys())}"),
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            payload['file_type'] = file.content_type
            payload['original_name'] = file.name
            payload['size'] = file.size
            payload['user'] = request.user

            serializer = FileSerializer(data=payload, context={"request": request})
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            file = validated_data["file"]
            file_obj = self.file_service.upload_file(request.user, file)
            serializer = FileSerializer(file_obj)
            result: Dict[str, Any] = {
                "message": _("Files uploaded successfully"),
                "data": serializer.data
            }

            self.daily_usage_service.update_daily_file_upload_count(request.user)

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "FileUploadView:post",
                         "message": "Unexpected error occurred", "error": str(e)})
            raise e
