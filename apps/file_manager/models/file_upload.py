from django.db import models
from django.contrib.auth import get_user_model
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.utils.constants import UPLOAD_PATH
from dxh_common.utils.enums import FileType
from common.models import BaseModel

User = get_user_model()


class FileUpload(BaseModel):
    company = models.ForeignKey(
        "core.Company",
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_("Company"),
        related_name="company_file_uploads",
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        verbose_name=_("User"),
    )
    file = models.FileField(
        upload_to=UPLOAD_PATH, 
        verbose_name=_("File")
    )
    file_type = models.CharField(
        max_length=50,
        db_index=True,
        verbose_name=_("File Type"),
    )
    original_name = models.CharField(
        max_length=255, 
        db_index=True, 
        verbose_name=_("Original Name")
    )
    size = models.BigIntegerField(
        blank=False, 
        null=False, 
        verbose_name=_("Size")
    )

    class Meta:
        db_table = "file_manager_file_uploads"
        verbose_name = _("File upload")
        verbose_name_plural = _("File uploads")

    def __str__(self):
        return f"{self.original_name} ({self.file_type})"