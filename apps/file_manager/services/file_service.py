from datetime import date
import io
import os
import zipfile
from django.conf import settings
from django.core.files.storage import default_storage
from django.db.models import Q
from django.http import FileResponse, HttpResponse, HttpResponseRedirect
from dxh_common.base.base_service import BaseService, ServiceError

from apps.file_manager.repositories import FileRepository
from apps.user.services.user_service import UserService


class FileService(BaseService):
    def __init__(self):
        super().__init__(FileRepository())
        self.user_service = UserService()

    def get_files(self, user):
        try:
            files = self.list(user=user).order_by("-created_at")

            return files

        except Exception as e:
            raise ServiceError(f"Error retrieving user files: {str(e)}")

    def upload_file(self, user, file):
        try:
            file_path = self._save_file(file)

            file_obj = self.repository.create(
                user=user,
                file=file_path,
                file_type=file.name.split(".")[-1],
                original_name=file.name,
                size=file.size,
            )

            return file_obj

        except Exception as e:
            raise ServiceError(f"Error uploading file: {str(e)}")

    def download_file(self, file_id):
        try:
            file_obj = self.repository.get(id=file_id)
            if file_obj is None:
                raise ServiceError("File not found")

            file_field = file_obj.file

            if not file_field.name:
                raise ServiceError("File has no name.")

            if settings.DJANGO_DEFAULT_FILE_STORAGE == 's3':
                url = default_storage.url(file_field.name)

                return HttpResponseRedirect(url)
            else:
                file_path = file_field.path
                if not os.path.exists(file_path):
                    raise FileNotFoundError("The requested file does not exist.")
                return FileResponse(open(file_path, "rb"), as_attachment=True, filename=file_obj.original_name)

        except FileNotFoundError as fnf:
            raise ServiceError(f"Error downloading file: {str(fnf)}")
        except Exception as e:
            raise ServiceError(f"Error downloading file: {str(e)}")

    def delete_file(self, file_id, user):
        try:
            file_obj = self.repository.get(id=file_id)
            if file_obj:
                self._delete_file(file_obj.file.name)
            self.repository.delete(file_obj)
        except Exception as e:
            raise ServiceError(f"Error deleting file: {str(e)}")

    def batch_download_files(self, file_ids, user):
        try:
            files = self.repository.filter(id__in=file_ids, user=user)
            if not files.exists():
                raise FileNotFoundError("No files found to download.")

            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
                for file in files:
                    if default_storage.exists(file.file.name):
                        file_content = default_storage.open(
                            file.file.name).read()
                        zip_file.writestr(file.original_name, file_content)

            zip_filename = self._generate_zip_filename(files, user)

            response = HttpResponse(
                zip_buffer.getvalue(), content_type="application/zip")
            response["Content-Disposition"] = f'attachment; filename="{zip_filename}"'

            return response

        except FileNotFoundError as fnf:
            raise ServiceError(f"Error generating batch download: {str(fnf)}")
        except Exception as e:
            raise ServiceError(f"Error generating batch download: {str(e)}")

    def batch_delete_files(self, file_ids, user):
        try:
            files = self.repository.filter(id__in=file_ids, user=user)
            for file in files:
                self._delete_file(file.file.name)
            self.repository.delete(files)
        except Exception as e:
            raise ServiceError(f"Error deleting files in batch: {str(e)}")

    def search_files(self, user, query="", file_type=None):
        try:
            files = self.repository.filter(user=user)
            if query:
                files = files.filter(Q(original_name__icontains=query) | Q(
                    file_type__icontains=query))
            if file_type:
                files = files.filter(file_type=file_type)

            return files.order_by("-created_at")

        except Exception as e:
            raise ServiceError(f"Error searching files: {str(e)}")

    # Need to Delete
    def generate_download_response(self, file_obj):
        try:
            if not default_storage.exists(file_obj.file.name):
                raise FileNotFoundError("File not found.")

            file_content = default_storage.open(file_obj.file.name, "rb")
            response = HttpResponse(
                file_content, content_type="application/octet-stream")
            response["Content-Disposition"] = f'attachment; filename="{file_obj.original_name}"'

            return response

        except FileNotFoundError as fnf:
            raise ServiceError(
                f"Error generating download response: {str(fnf)}")
        except Exception as e:
            raise ServiceError(f"Error generating download response: {str(e)}")

    def _save_file(self, file):
        if settings.DJANGO_DEFAULT_FILE_STORAGE == 's3':
            return default_storage.save(file.name, file)

        else:
            file_path = os.path.join(settings.MEDIA_ROOT, file.name)
            with open(file_path, "wb+") as destination:
                for chunk in file.chunks():
                    destination.write(chunk)

            return file_path

    def _delete_file(self, file_name):
        if default_storage.exists(file_name):
            default_storage.delete(file_name)

    def _generate_zip_filename(self, files, user):
        if len(files) == 1:
            return f"{files[0].original_name.split('.')[0]}.zip"

        return f"files_{user.pk}.zip"

    def save_external_file(self, user_id, file_content, filename, file_type, size):
        try:
            today = date.today()
            user = self.user_service.get(id=user_id)
            if not user:
                raise ServiceError("User not found")

            # Fix: Access day as an attribute, not a method
            year = today.year
            month = today.month
            day = today.day
            file_path = default_storage.save(f"generated-images/user_{user_id}/{year}/{month}/{day}/{filename}", file_content)

            file_obj = self.create(
                user=user,
                file=file_path,
                file_type=file_type.lower(),
                original_name=filename,
                size=size,
            )

            return file_obj

        except Exception as e:
            raise ServiceError(f"Error uploading external file: {str(e)}")