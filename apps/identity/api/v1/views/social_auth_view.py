from django.contrib.auth.hashers import is_password_usable
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import status, Response
from dxh_common.logger import Logger
from dxh_libraries.rest_framework_simplejwt import RefreshToken

from dj_rest_auth.registration.views import Social<PERSON>oginView
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.facebook.views import FacebookOAuth2Adapter

from apps.user.api.v1.serializers.user_serializers import UserSerializer
from config.settings import GOOGLE_CALLBACK_URL, FACEBOOK_CALLBACK_URL

from apps.identity.utils.client import CustomOAuth2Client
from apps.user.services.user_preference_service import UserPreferenceService
from apps.identity.services.identity_service import IdentityService


logger = Logger(__name__)


class GoogleAuthView(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = GOOGLE_CALLBACK_URL
    client_class = CustomOAuth2Client

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()
        self.identity_service = IdentityService()

    def get_response(self):
        """
        Overrides the default response to create a UserPreference after login.
        """
        response = super().get_response()
        user = self.user

        if not is_password_usable(user.password):
            user.set_unusable_password()
            user.save()

        self.identity_service.save_profle_picture_and_name(user)
        # Create or update user preference
        self.user_preference_service.get_or_create_user_preference(user)
        user_data = UserSerializer(user).data

        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        refresh_token = str(refresh)

        result = {
            "message": _("Login successful."),
            "data": {
                "tokens": {
                    "access": access_token,
                    "refresh": refresh_token,
                },
                "user": user_data,
            },
        }
        logger.info(
            {"event": "GoogleAuthView:get", "message": "Google login successfully"}
        )
        return Response(result, status=status.HTTP_200_OK)


class FacebookAuthView(SocialLoginView):
    adapter_class = FacebookOAuth2Adapter
    client_class = CustomOAuth2Client
    callback_url = FACEBOOK_CALLBACK_URL

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_preference_service = UserPreferenceService()

    def get_response(self):
        """
        Overrides the default response to create a UserPreference after login.
        """
        response = super().get_response()
        user = self.user  # Authenticated user

        # Create or update user preference
        self.user_preference_service.get_or_create_user_preference(user)
        user_data = UserSerializer(user).data

        result = {
            "message": _("Login successful."),
            "data": {
                "tokens": {
                    "access": response.data.get("access"),
                    "refresh": response.data.get("refresh"),
                },
                "user": user_data,
            },
        }
        logger.info(
            {"event": "FacebookAuthView:get", "message": "Facebook login successfully"}
        )
        return Response(result, status=status.HTTP_200_OK)
