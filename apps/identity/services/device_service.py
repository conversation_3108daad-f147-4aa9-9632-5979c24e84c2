import user_agents
from django.utils import timezone
from common.logger import Logger
from common.service import BaseService, ServiceError
from apps.identity.repositories.device_repository import DeviceRepository
from apps.identity.utils.device import generate_device_id  

logger = Logger(__name__)

class DeviceService(BaseService):
    def __init__(self):
        repository = DeviceRepository()
        super().__init__(repository)
        self.device_repository = repository

    def register_device(self, user, request):
        try:
            # Extract User-Agent from the request
            ua_string = request.META.get('HTTP_USER_AGENT', '')
            logger.info(f"Raw User Agent: {ua_string}")

            # Extract IP address from the request
            ip_address = self._get_client_ip(request)
            logger.info(f"Client IP Address: {ip_address}")

            # Parse User-Agent and get device details
            user_agent = user_agents.parse(ua_string)
            device_name = self._get_device_name(user_agent)
            browser_info = self._get_browser_info(user_agent)
            logger.info(f"Detected Device: {device_name}")
            logger.info(f"Detected Browser: {browser_info}")

            # Generate a unique device identifier
            device_identifier = generate_device_id(browser_info, user_agent.os.family, user.id)
            logger.info(f"Generated Device ID: {device_identifier}")

            # Mark all other devices as not current
            devices = self.device_repository.filter(user=user)
            if devices.exists():
                devices.update(is_current_device=False)
                logger.info(f"Marked all other devices as not current for user {user.id}")
            else:
                logger.info(f"No devices found for user {user.id} to mark as not current.")

            # Check if the device already exists
            current_device = self.device_repository.filter(device_id=device_identifier).first()
            if current_device:
                current_device.is_current_device = True
                current_device.last_login = timezone.now()
                current_device.ip_address = ip_address
                current_device.save()
                logger.info(f"Updated existing device {device_identifier} as current for user {user.id}")
                return current_device
            else:
                # Create a new device entry
                new_device = self.device_repository.model.objects.create(
                    user=user,
                    device_id=device_identifier,
                    device_name=device_name,
                    ip_address=ip_address,
                    is_current_device=True,
                    last_login=timezone.now()
                )
                logger.info(f"Created and marked new device {device_identifier} as current for user {user.id}")
                return new_device

        except Exception as e:
            logger.error(f"Error registering device for user {user.id}: {str(e)}")
            raise ServiceError(f"Error registering device for user {user.id}: {str(e)}")

    def _get_device_name(self, user_agent):
        """Get a user-friendly device name"""
        try:
            raw_ua = user_agent.ua_string.lower()
            logger.info(f"Processing User Agent: {raw_ua}")

            # API Client detection
            if any(client in raw_ua for client in ['postman', 'postmanruntime']):
                logger.info("Detected Postman client")
                return "Postman Client"
            
            device_info = {
                'os_family': user_agent.os.family,
                'os_version': user_agent.os.version_string,
                'browser_family': user_agent.browser.family,
                'device_brand': user_agent.device.brand,
                'device_model': user_agent.device.model
            }
            logger.info(f"Device info: {device_info}")

            if user_agent.is_pc:
                return f"{device_info['os_family']} Computer"
            elif user_agent.is_mobile:
                return f"{device_info['device_brand'] or 'Mobile'} {device_info['device_model'] or 'Device'}"
            elif user_agent.is_tablet:
                return f"{device_info['device_brand'] or ''} Tablet"
            else:
                return f"{device_info['browser_family']} Client"

        except Exception as e:
            logger.error(f"Device detection error: {str(e)}", exc_info=True)
            return f"{user_agent.browser.family} Client"

    def _get_browser_info(self, user_agent):
        """Get formatted browser information"""
        return f"{user_agent.browser.family} {user_agent.browser.version_string}"

    def get_user_devices(self, user_id):
        return self.device_repository.get_user_devices(user_id)

    def _get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    def delete_device(self, device_id):
        try:
            self.device_repository.delete_device(device_id)
            logger.info(f"Device deleted successfully: {device_id}")
        except Exception as e:
            logger.error(f"Error deleting device {device_id}: {str(e)}")
            raise ServiceError(f"Error deleting device: {str(e)}")

    def delete_all_devices(self, user_id, except_device_id=None):
        try:
            self.device_repository.delete_all_devices(user_id, except_device_id)
            logger.info(f"All devices deleted for user: {user_id}")
        except Exception as e:
            logger.error(f"Error deleting devices for user {user_id}: {str(e)}")
            raise ServiceError(f"Error deleting devices: {str(e)}")

    def _get_client_ip(self, request):
        """Extract the client IP address from the request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip