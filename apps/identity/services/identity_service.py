from urllib.parse import urlencode

import requests
from django.conf import settings
from django.utils import timezone
from django.utils.formats import date_format
from django.db import transaction
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.utils.translation import gettext_lazy as _

from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from social_django.utils import load_backend, load_strategy
from allauth.socialaccount.models import SocialAccount

from common.logger import Logger
from common.utils.shared_utils import generate_otp
from common.service import BaseService
from common.service import ServiceError
from common.validators import validate_password_strength
from common.constants import VerificationTokenType, ValidityPeriod
from apps.cross_cutting.email_service import EmailService
from apps.identity.repositories.user_repository import UserRepository
from apps.identity.repositories.verification_code_repository import VerificationCodeRepository
from apps.notification.services.notification_service import NotificationService
from apps.identity.services.device_service import DeviceService
from apps.identity.models.user_device import UserDevice

logger = Logger(__name__)


class VerificationCodeService(BaseService):
    def __init__(self):
        super().__init__(VerificationCodeRepository())

    def get_verification_code(self, code):
        try:
            return self.repository.get(code=code)
        except Exception as e:
            logger.error(f"Error getting verification code with code {code}: {str(e)}")
            raise ServiceError(f"Error getting verification code: {str(e)}")

    def create_verification_code(self, user, code_type):
        try:
            code = generate_otp()
            expiry = ValidityPeriod.OTP.value
            expire_at = timezone.now() + timezone.timedelta(minutes=expiry)

            self.create(
                user=user,
                code=code,
                type=code_type,
                expires_at=expire_at,
            )
            logger.info(f"Verification code created for {user.email}")
            
            return code

        except Exception as e:
            logger.error(f"Error creating verification code for {user.email}: {str(e)}")
            raise ServiceError(f"Error creating verification code: {str(e)}")

class IdentityService(BaseService):
    def __init__(self):
        super().__init__(UserRepository())
        self.verification_code_service = VerificationCodeService()
        self.email_service = EmailService()
        self.device_service = DeviceService()

    def login(self, email, password, request):
        try:
            user = self.repository.get(email=email) 

            if not user:
                logger.error(f"Error logging in user with email {email}: Invalid credentials")
                return None, None, "Invalid credentials"

            if not user.is_active:
                logger.error(f"Error logging in user with email {email}: User not active")
                return None, None, "User is not active"
            
            if user.is_deleted:
                logger.error(f"Error logging in user with email {email}: User is deleted")
                return None, None, "No active user found with this email"
            
            if not user.check_password(password):
                logger.error(f"Error logging in user with email {email}: Invalid password")
                return None, None, "Invalid credentials"

            # Register device
            device = self.device_service.register_device(user=user, request=request)
                
            # Update current device status
            UserDevice.objects.filter(user=user).update(is_current_device=False)
            device.is_current_device = True
            device.save()

            refresh = RefreshToken.for_user(user)
            tokens = {"access": str(refresh.access_token), "refresh": str(refresh)}
            
            logger.info(f"User logged in successfully with email {email}")
            return user, tokens, None

        except Exception as e:
            logger.error(f"Error logging in user with email {email}: {str(e)}")
            raise ServiceError(f"Error logging in: {str(e)}")

    def save_profle_picture_and_name(self, user):
        try:
            social_account = SocialAccount.objects.filter(user=user).first()
            google_data = social_account.extra_data
            if not user.name:
                user.name = google_data.get("name", None)
            profile_picture_url = google_data.get("picture", None)
            if profile_picture_url and not user.profile_picture:
                self.download_and_save_profile_picture(user, profile_picture_url)

            user.save()

        except SocialAccount.DoesNotExist:
            logger.error(f"Social account not found for user {user.email}")
            return None
        
    def download_and_save_profile_picture(self, user, url):
        try:
            # Download the image
            response = requests.get(url)
            response.raise_for_status()

            # Generate a unique file name
            file_name = f"profile_{user.email}.jpg"

            user.profile_picture.save(
                file_name,
                ContentFile(response.content),
                save=False
            )
            logger.info(f"Profile picture saved for user {user.email}")

        except requests.RequestException as e:
            logger.error(f"Failed to download profile picture for user {user.email}: {e}")
            raise ServiceError(f"Failed to download profile picture for user {user.email}: {e}")
        
    def social_auth(self, request, provider):
        try:
            strategy = load_strategy(request)
            backend = load_backend(strategy, provider, None)
            
            return backend.auth_url()

        except Exception as e:
            logger.error(f"Error initiating social authentication with {provider}: {str(e)}")
            raise ServiceError(f"Error initiating social authentication: {str(e)}")

    def verify_social_token(self, provider, token):
        try:
            social_account = SocialAccount.objects.get(
                provider=provider, uid=token["sub"]
            )
            return social_account.user

        except Exception as e:
            logger.error(f"Error verifying social token for provider {provider}: {str(e)}")
            raise ServiceError(f"Error verifying social token: {str(e)}")

    def verify_email(self, code):
        try:
            verification_code = self.verification_code_service.get(
                code=code, 
                type=VerificationTokenType.EMAIL_OTP.value
            )
            if not verification_code or verification_code.expires_at < timezone.now():
                return False

            user = verification_code.user
            user.is_email_verified = True
            user.is_active = True
            user.save()
            verification_code.delete()
            logger.info(f"Email verification successful for user {user.email}")
            
            return True

        except Exception as e:
            logger.error(f"Error verifying email with code {code}: {str(e)}")
            raise ServiceError(f"Error verifying email: {str(e)}")
        
    def verify_otp(self, email, otp):
        user = self.repository.get(email=email)
        if not user:
            return None
        
        otp_entry = self.validate_otp(user, otp)
        if otp_entry:
            user.is_active = True
            user.is_deleted = False
            user.save()
            return user

        return None

    def validate_otp(self, user, otp_code):

        otp_entry = self.verification_code_service.get(
            user=user, 
            type=VerificationTokenType.EMAIL_OTP.value,
            code=otp_code,
            is_used=False,
        )
        if not otp_entry or otp_entry.expires_at < timezone.now():
            return False

        otp_entry.is_used = True
        otp_entry.save()
        
        return otp_entry

    def verify_phone(self, code):
        try:
            verification = self.verification_code_service.get(
                code=code,
                type=VerificationTokenType.PASSWORD_RESET_OTP.value,
                is_used=False,
                expires_at__gte=timezone.now(),
            )
            
            if verification:
                verification.user.is_phone_verified = True
                verification.user.save()
                verification.is_used = True
                verification.save()
                logger.info(f"Phone verification successful for user {verification.user.email}")
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"Error verifying phone with code {code}: {str(e)}")
            raise ServiceError(f"Error verifying phone: {str(e)}")

    def send_verification_email(self, user, code):
        try:
            expiry = ValidityPeriod.OTP.value
            
            # Get display name
            display_name = user.name if hasattr(user, 'name') and user.name else user.email.split('@')[0].split('.')[0].capitalize()
            
            self.email_service.send_email(
                to_email=user.email,
                subject="Verify your email",
                template_name="email_verification_otp",
                context={
                    "user": {
                        "id": user.pk,
                        "name": display_name,
                        "email": user.email,
                    }, 
                    "otp": code, 
                    "expiry": expiry
                }
            )
            logger.info(f"Verification email sent to {user.email}")

        except Exception as e:
            logger.error(f"Error sending verification email to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending verification email: {str(e)}")
        
    def resend_otp_email(self, user, code):
        try:
            expiry = ValidityPeriod.OTP.value
            
            # Get display name
            display_name = user.name if hasattr(user, 'name') and user.name else user.email.split('@')[0].split('.')[0].capitalize()
            
            self.email_service.send_email(
                to_email=user.email,
                subject="Resend OTP",
                template_name="email_verification_otp",
                context={
                    "user": {
                        "id": user.pk,
                        "name": display_name,
                        "email": user.email,
                    }, 
                    "otp": code, 
                    "expiry": expiry
                }
            )
            logger.info(f"Resend OTP email sent to {user.email}")

        except Exception as e:
            logger.error(f"Error sending resend OTP email to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending resend OTP email: {str(e)}")

    def refresh_token(self, refresh_token):
        try:
            refresh = RefreshToken(refresh_token)
            tokens = {
                "access": str(refresh.access_token),
                "refresh": str(refresh),
            }
            logger.info("Token refreshed successfully")
            return tokens
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise ServiceError(f"Error refreshing token: {str(e)}")
        
    def _send_reset_password_email(self, user, code):
        try:
            token = AccessToken.for_user(user)
            expiry = ValidityPeriod.OTP.value

            # Get display name
            display_name = user.name if hasattr(user, 'name') and user.name else user.email.split('@')[0].split('.')[0].capitalize()

            params = {
                "code": code,
                "token": str(token),  # Convert token to string
                "email": user.email
            }
            reset_link = f"{settings.FRONTEND_BASE_URL}/auth/new-password?{urlencode(params)}"

            context = {
                "user": {
                    "id": user.pk,
                    "name": display_name,
                    "email": user.email,
                },
                "reset_link": reset_link,
                "otp": code,
                "expiry": expiry,
            }

            self.email_service.send_email(
                to_email=user.email,
                subject="Reset Your Password",
                template_name="password_reset_link",
                context=context
            )
            logger.info(f"Reset password link sent to {user.email}")
        except Exception as e:
            logger.error(f"Error sending reset password link to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending reset password link: {str(e)}")
        

    def forgot_password(self, email):
        try:
            user = self.repository.get(email=email)
            if not user:
                return False
            type = VerificationTokenType.PASSWORD_RESET_OTP.value
            code = self.verification_code_service.create_verification_code(user, code_type=type)

            # Send OTP via Email
            self._send_reset_password_email(user=user, code=code)

            logger.info(f"Forgot password email sent to {user.email}")
            return True

        except Exception as e:
            logger.error(f"Error sending forgot password email to {email}: {str(e)}")
            raise ServiceError(f"Error sending forgot password email: {str(e)}")

    def reset_password(self, user, new_password):
        try:
            validate_password_strength(new_password)
        except ValidationError as e:
            return False, str(list(e)[0])

        try:
            user.set_password(new_password)
            user.save()
            logger.info(f"Password reset successful for user {user.email}")
            return True, "Password reset successful"

        except Exception as e:
            logger.error(f"Error resetting password with email {user.email}: {str(e)}")
            raise ServiceError(f"Error resetting password: {str(e)}")

    @transaction.atomic
    def change_password(self, user, old_password, new_password):
        try:
            validate_password_strength(new_password)
        except ValidationError as e:
            raise ServiceError(f"Invalid password: {str(list(e)[0])}")

        if not user.check_password(old_password):
            return False, "Old password is incorrect"

        user.set_password(new_password)
        user.save()
        logger.info(f"Password changed successfully for user {user.email}")
        
        return True, "Password changed successfully"
    
    def set_new_password(self, user, new_password):
        """
        Sets a new password for users with unusable passwords.
        """
        if user.has_usable_password():
            return False, _("This user already has a usable password.")

        user.set_password(new_password)
        user.save()
        return True, _("New password set successfully.")
    
    def handle_social_callback(self, provider, code):
        try:
            strategy = load_strategy()
            backend = load_backend(strategy, provider, redirect_uri=None)
            user = backend.do_auth(code)

            if not user:
                return None, "Authentication failed"

            logger.info(f"User authenticated via social login with {provider}")
            return user, None

        except Exception as e:
            logger.error(
                f"Error during authentication for provider {provider}: {str(e)}"
            )
            raise ServiceError(f"Error during authentication: {str(e)}")

    def send_verification_link(self, user, code):
        try:
            expiry = ValidityPeriod.OTP.value
            
            # Get display name
            display_name = user.name if hasattr(user, 'name') and user.name else user.email.split('@')[0].split('.')[0].capitalize()
            
            logger.info(f"Preparing to send verification email to {user.email} with code {code}")
            
            # Create a simple, serializable context
            context = {
                "user": {
                    "id": user.pk,
                    "name": display_name,
                    "email": user.email,
                },
                "otp": code,
                "expiry": expiry,
                "first_name": display_name
            }
            
            self.email_service.send_email(
                to_email=user.email,
                subject="Verify your email",
                template_name="email_verification_link",
                context=context
            )
            logger.info(f"Verification email queued for {user.email}")
        except Exception as e:
            logger.error(f"Error sending verification email to {user.email}: {str(e)}")
            raise ServiceError(f"Error sending verification email: {str(e)}")

    def create_verification_code(self, user):
        try:
            code = self.verification_code_service.create_verification_code(user)
            logger.info(f"Verification code created for {user.email}")
            
            return code
        except Exception as e:
            logger.error(f"Error creating verification code for {user.email}: {str(e)}")
            raise ServiceError(f"Error creating verification code: {str(e)}")

    def send_customer_query(self, user, message, request):
        try:
            # Format current time
            submitted_time = timezone.localtime()
            formatted_time = date_format(submitted_time, format="DATETIME_FORMAT")
            
            # Get IP address
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')

            context = {
                "name": user.get_full_name() or user.email,
                "email": user.email,
                "message": message,
                "submitted_on": formatted_time,
                "ip_address": ip_address
            }

            self.email_service.send_email(
                to_email=settings.CUSTOMER_SUPPORT_EMAIL,
                subject="New Customer Query",
                template_name="customer_query",
                context=context
            )
        except Exception as e:
            logger.error(f"Error sending customer query: {str(e)}")
            raise ServiceError(f"Error sending customer query: {str(e)}")
