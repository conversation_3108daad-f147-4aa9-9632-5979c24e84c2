import pyotp
from django.conf import settings
from common.logger import Logger
from common.constants import MFAType
from common.service import BaseService, ServiceError
from apps.identity.repositories.mfa_setup_repository import MFASetupRepository
from apps.identity.utils.mfa import generate_backup_code

logger = Logger(__name__)

class MFAService(BaseService):

    def __init__(self):
        super().__init__(MFASetupRepository())

    def create_mfa_setup(self, user):
        secret = pyotp.random_base32()
        backup_codes = [generate_backup_code() for _ in range(10)]

        return self.mfa_setup.create(
            user=user,
            type=MFAType.TOTP.value,
            secret=secret,
            backup_codes=backup_codes,
        )

    def get_mfa_setup(self, data):
        """
        Retrieve the MFA setup details based on the given data.
        """
        try:
            return self.repository.get(**data)
        except Exception as e:
            logger.error(f"Error fetching MFA setup with data {data}: {str(e)}")
            raise ServiceError(f"Failed to retrieve MFA setup: {str(e)}")

    def setup_mfa(self, user):
        """
        Set up MFA for the user, generating a secret and backup codes.
        Returns the secret, backup codes, and QR provisioning URI.
        """
        try:
            secret = pyotp.random_base32()
            backup_codes = [generate_backup_code() for _ in range(10)]

            # Create MFA setup in the repository
            self.repository.create(
                user=user,
                type=MFAType.TOTP.value,
                secret=secret,
                backup_codes=backup_codes,
            )

            # Create the TOTP object and get provisioning URI
            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(user.email, issuer_name=settings.APP_NAME)

            return {
                "secret": secret,
                "backup_codes": backup_codes,
                "qr_uri": provisioning_uri,
            }

        except Exception as e:
            logger.error(f"Error setting up MFA for user {user.pk}: {str(e)}")
            raise ServiceError(f"Failed to set up MFA: {str(e)}")