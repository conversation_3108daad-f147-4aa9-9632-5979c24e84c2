import requests
from social_django.utils import load_strategy, load_backend
from allauth.socialaccount.models import SocialAccount
from common.logger import Logger
from common.service import BaseService, ServiceError
from common.constants import GOOGLE_TOKENINFO_URL, FACEBOOK_GRAPH_URL
from apps.identity.repositories.user_repository import UserRepository

logger = Logger(__name__)

class SocialService(BaseService):

    def __init__(self):
        super().__init__(UserRepository()) 

    def get_auth_url(request, provider):
        strategy = load_strategy(request)
        backend = load_backend(strategy, provider, None)
        
        return backend.auth_url()

    def verify_token(provider, token):
        try:
            social_account = SocialAccount.objects.get(
                provider=provider,
                uid=token['sub']
            )
            return social_account.user, None
        
        except SocialAccount.DoesNotExist:
            logger.error(f"Account not found for provider {provider} with UID {token['sub']}")
            return None, "Account not found"
        
        except Exception as e:
            logger.error(f"Error verifying token for provider {provider}: {str(e)}")
            raise ServiceError(f"Error verifying token: {str(e)}")

    def create_social_user(self, provider, data):
        try:
            email = data.get('email')
            user = self.repository.create(
                email=email,
                username=email,
                first_name=data.get('given_name', ''),
                last_name=data.get('family_name', ''),
                is_email_verified=True
            )
            SocialAccount.objects.create(
                user=user,
                provider=provider,
                uid=data['sub']
            )
            return user, None
        
        except Exception as e:
            logger.error(f"Error creating social user for provider {provider}: {str(e)}")
            raise ServiceError(f"Failed to create social user: {str(e)}")

    def google_auth(self, token):
        try:
            # Validate the token with Google
            response = requests.get(
                GOOGLE_TOKENINFO_URL,
                params={'id_token': token}
            )
            response_data = response.json()

            if 'error_description' in response_data:
                logger.error(f"Google auth error: {response_data['error_description']}")
                return None, response_data['error_description']

            email = response_data['email']
            first_name = response_data.get('given_name', '')
            last_name = response_data.get('family_name', '')

            # Check if the user already exists
            existing_user = self.repository.filter(email=email)
            if not existing_user:
                user = self.repository.create(
                    email=email,
                    defaults={
                        'username': email,
                        'first_name': first_name,
                        'last_name': last_name,
                        'is_email_verified': True
                    }
                )
                SocialAccount.objects.create(
                    user=user,
                    provider='google',
                    uid=response_data['sub']
                )

            return user, None
        
        except Exception as e:
            logger.error(f"Error during Google authentication: {str(e)}")
            raise ServiceError(f"Failed to authenticate with Google: {str(e)}")

    def facebook_auth(self, token):
        try:
            # Validate the token with Facebook
            response = requests.get(
                FACEBOOK_GRAPH_URL,
                params={
                    'access_token': token,
                    'fields': 'id,email,first_name,last_name'
                }
            )
            response_data = response.json()

            if 'error' in response_data:
                logger.error(f"Facebook auth error: {response_data['error']['message']}")
                return None, response_data['error']['message']

            email = response_data.get('email')
            first_name = response_data.get('first_name', '')
            last_name = response_data.get('last_name', '')

            if not email:
                # Handle the case where email is not provided
                email = f"{response_data['id']}@facebook.com"

            # Check if the user already exists
            existing_user = self.repository.filter(email=email)
            if not existing_user:
                user = self.repository.create(
                    email=email,
                    defaults={
                        'username': email,
                        'first_name': first_name,
                        'last_name': last_name,
                        'is_email_verified': True
                    }
                )
                SocialAccount.objects.create(
                    user=user,
                    provider='google',
                    uid=response_data['sub']
                )

            return user, None
        
        except Exception as e:
            logger.error(f"Error during Facebook authentication: {str(e)}")
            raise ServiceError(f"Failed to authenticate with Facebook: {str(e)}")
