from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView

from apps.llm_manager.services.conversation import ConversationService
from apps.llm_manager.api.v1.serializers import ChatSearchSerializer

class ChatSearchView(BaseApiView):

    def get(self, request):
        query = request.query_params.get('q', '')

        # Fetch conversations that match the search query
        conversation_service = ConversationService()
        conversations = conversation_service.search_user_conversations(request.user, query)

        # Serialize response
        serializer = ChatSearchSerializer(conversations, many=True)
        return Response({
            "success": True,
            "statusCode": 200,
            "data": serializer.data
        })