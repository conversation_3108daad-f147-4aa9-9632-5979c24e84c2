from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger
from dxh_libraries.drf_yasg import swagger_auto_schema

from apps.llm_manager.api.v1.serializers.custom_gpt_serializer import CustomGPTSerializer
from apps.llm_manager.services.custom_gpt_service import CustomGPTService

logger = Logger(__name__)

class CustomGPTView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.custom_gpt_service = CustomGPTService()
        
    def get(self, request, id=None):
        try:
            if id:
                custom_gpt = self.custom_gpt_service.get(id=id, user=request.user)
                if not custom_gpt:
                    result = {
                        "message": _("Custom GPT not found")
                    }
                    return Response(result, status=status.HTTP_404_NOT_FOUND)
                    
                serializer = CustomGPTSerializer(custom_gpt)
                result = {
                    "message": _("Custom GPT retrieved successfully"),
                    "data": serializer.data
                }
                return Response(result, status=status.HTTP_200_OK)
            
            custom_gpts = self.custom_gpt_service.list(user=request.user).order_by("-created_at")
            serializer = CustomGPTSerializer(custom_gpts, many=True)
            result = {
                "message": _("Custom GPTs retrieved successfully"), 
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error retrieving custom GPTs: {str(e)}")
            raise e
 
    @swagger_auto_schema(request_body=CustomGPTSerializer)
    def post(self, request):
        try:
            if request.user.subscription_type == "free":
                result = {
                    "message": _("Custom GPTs are only available for premium users")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)
                
            serializer = CustomGPTSerializer(data=request.data)
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
                
            custom_gpt = self.custom_gpt_service.create(
                user=request.user,
                name=serializer.validated_data["name"],
                instructions=serializer.validated_data["instructions"]
            )
            
            serializer = CustomGPTSerializer(custom_gpt)
            result = {
                "message": _("Custom GPT created successfully"), 
                "data": serializer.data
            }
            return Response(result, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error creating custom GPT: {str(e)}")
            raise e
    
    @swagger_auto_schema(request_body=CustomGPTSerializer)
    def put(self, request, id):
        try:
            custom_gpt = self.custom_gpt_service.get(id=id, user=request.user)
            if not custom_gpt:
                result = {
                    "message": _("Custom GPT not found")
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)
                
            serializer = CustomGPTSerializer(data=request.data, partial=True)
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
                
            custom_gpt = self.custom_gpt_service.update(
                custom_gpt,
                **serializer.validated_data
            )
            
            serializer = CustomGPTSerializer(custom_gpt)
            result = {"message": _("Custom GPT updated successfully"), "data": serializer.data}

            return Response(result , status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error updating custom GPT: {str(e)}")
            raise e
    
    def delete(self, request, id):
        try:
            custom_gpt = self.custom_gpt_service.get(id=id, user=request.user)
            if not custom_gpt:
                result = {
                    "message": _("Custom GPT not found")
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)
                
            self.custom_gpt_service.delete(custom_gpt)
            result = {"message": _("Custom GPT deleted successfully")}

            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error deleting custom GPT: {str(e)}")
            raise e
