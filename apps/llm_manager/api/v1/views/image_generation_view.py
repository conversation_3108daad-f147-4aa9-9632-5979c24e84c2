from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger
from django.utils.translation import gettext_lazy as _

from apps.llm_manager.api.v1.serializers import GeneratedImageSerializer, PromptSerializer
from apps.llm_manager.services import ImageGenerationService, ConversationService, CustomGPTService, AIHandlerFactory
from apps.llm_manager.utils.response_formater import format_ai_response
from apps.user.services.daily_message_usage_service import DailyMessageUsageService

logger = Logger(__name__)


class ImageGenerationView(BaseApiView):
    """
    View for generating images from text prompts
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.image_generation_service = ImageGenerationService()
        self.conversation_service = ConversationService()
        self.custom_gpt_service = CustomGPTService()
        self.ai_factory_service = AIHandlerFactory()
        self.daily_usage_service = DailyMessageUsageService()
    
    def post(self, request):
        """Generate an image from a text prompt"""
        try:
            payload = request.data
            serializer = PromptSerializer(data=payload)
            if not serializer.is_valid():
                first_error_message = serializer.errors[next(iter(serializer.errors))][0]
                result = {
                    "message": first_error_message,
                    "errors": serializer.errors
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            can_send, current_count, max_limit = self.daily_usage_service.check_daily_image_generation_limit(request.user)
            if not can_send:
                return Response(
                    {"message": f"You've hit your daily image generation limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan!"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            validated_data = serializer.validated_data
            content = validated_data["message"]["content"]
            prompt = content["parts"][0]
            size = content.get("size") or "1024x1024"
            style = content.get("style") or "natural"

            conversation, user_message, prompt_data = self.conversation_service.create_conversation_and_message(
                request.user, validated_data, custom_gpt=None
            )

            generated_image, error = self.image_generation_service.generate_image(
                user=request.user,
                prompt=prompt,
                size=size,
                style=style,
                conversation=conversation,
                user_message=user_message
            )

            self.daily_usage_service.update_daily_image_generation_count(request.user)
            
            if error:
                result = {"message": _(error)}
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            
            
            result = {
                "message": _("Image generated successfully"),
                "data": format_ai_response(generated_image)
            }
            
            return Response(result, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error({"event": "ImageGenerationView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e
    
    def get(self, request):
        """Get all images generated by the user"""
        try:
            images = self.image_generation_service.get_user_images(request.user)
            serializer = GeneratedImageSerializer(images, many=True)
            
            result = {
                "message": _("Images retrieved successfully"),
                "data": serializer.data
            }
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error({"event": "ImageGenerationView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e
