from apps.cross_cutting.ai_service import <PERSON>Seek<PERSON><PERSON><PERSON>, OpenAIHandler, LamaAIHandler, OpenAIImageHandler
from apps.llm_manager.services.llm_models_service import LLMModelsService

class AIHandlerFactory:
    """Factory to create AI handler instances based on the selected provider."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.llm_model_service = LLMModelsService()

    def get_handler(self, model_name):
        handlers = {
            "gpt-4o-mini": lambda: OpenAIHandler(model_name),
            "gpt-4o": lambda: OpenAIHandler(model_name),
            "gpt-4": lambda: <PERSON>AI<PERSON><PERSON>ler(model_name),
            "deepseek-chat": lambda: DeepSeek<PERSON><PERSON><PERSON>(model_name),
            "llama3.1-70b": lambda: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(model_name),
            "deepseek-r1": lambda: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(model_name),
            "dall-e-3": lambda: OpenA<PERSON><PERSON><PERSON><PERSON><PERSON>(model_name),
        }
        return handlers.get(model_name, lambda: OpenAIH<PERSON>ler("gpt-4o"))()
