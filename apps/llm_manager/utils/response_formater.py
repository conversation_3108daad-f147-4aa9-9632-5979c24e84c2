from datetime import datetime

# Modify format_ai_response function to include attachments
def format_ai_response(ai_response_obj, status=None, from_redis=False, conversation_id=None, msg_id=None):
    if from_redis:
        data = {
            "conversation_id": conversation_id,
            "model": "gpt-4o-mini",
            "action": "response",
            "parent_message_id": None,
            "message": {
                "author": {"role": "assistant"},
                "create_time": str(datetime.now()),
                "id": msg_id,
                "content": {
                    "content_type": "text",
                    "parts": [ai_response_obj]
                },
                "metadata": {
                    "attachments": []  # Include attachments if any
                }
            }
        }
    else:
        response_content = ai_response_obj.content
        responsed_at = ai_response_obj.created_at

        attachments = ai_response_obj.attachments.all() if hasattr(ai_response_obj, 'attachments') else []
        attachment_list = [
            {
                "id": att.file.id,
                "file_url": att.file.file.url,
                "file_type": att.file.file_type,
                "original_name": att.file.original_name,
                "size": att.file.size,
                "created_at": att.file.created_at.isoformat() if att.file.created_at else None
            } for att in attachments
        ]
        if ai_response_obj.llm_model:
            model_code = ai_response_obj.llm_model.code
        else:
            model_code = "dall-e-3"

        data = {
            "conversation_id": str(ai_response_obj.conversation.shareable_uuid) if status == 'is_public' else str(ai_response_obj.conversation.uuid),
            "model": model_code,
            "action": "response",
            "parent_message_id": str(ai_response_obj.parent.uuid) if ai_response_obj.parent else None,
            "message": {
                "author": {"role": ai_response_obj.author},
                "create_time": str(responsed_at),
                "id": str(ai_response_obj.uuid),
                "content": {
                    "content_type": ai_response_obj.content_type,
                    "parts": [response_content]
                },
                "metadata": {
                    "attachments": attachment_list,  # Include attachments here
                    "generated_image": ai_response_obj.metadata.get("generated_image", {})
                }
            }
        }

    return data
