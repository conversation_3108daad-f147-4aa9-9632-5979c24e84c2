from abc import ABC, abstractmethod
from django.core.exceptions import PermissionDenied
from apps.user.services.daily_token_uses_service import DailyTokenUsagesService
from apps.payment.services.subscription_service import SubscriptionService
from apps.payment.services.plan_service import PlanService
from dxh_common.logger import Logger

logger = Logger(__name__)


class TokenUsagePolicy(ABC):
    """
    Abstract base class for defining token usage policies.
    """
    @abstractmethod
    def check_token_usage(self, user, input_tokens, model_code=None):
        """
        Check if the user is allowed to use the given number of tokens.
        Raises an exception if the limit is exceeded.

        Args:
            user: The user to check
            input_tokens: The number of tokens in the input
            model_code: The model code being used (to check if it's already gpt-4o-mini)
        """
        pass


class FreeUserTokenUsagePolicy(TokenUsagePolicy):
    """
    Token usage policy for free users.
    """
    def __init__(self):
        self.daily_token_service = DailyTokenUsagesService()
        self.plan_service = PlanService()
        self.subscription_service = SubscriptionService()

    def check_token_usage(self, user, input_tokens, model_code=None):
        # Get the user's monthly token usage
        total_used_tokens = self.daily_token_service.get_monthly_token_usage(user)

        # Get the user's token limit from their plan
        free_plan = self.plan_service.get(code="free")
        token_limit = free_plan.tokens_included if free_plan else 50000  # Fallback to default

        logger.info(f"Checking token usage for user {user.id}: used={total_used_tokens}, input={input_tokens}, limit={token_limit}, model={model_code}")

        if total_used_tokens + input_tokens > token_limit:
            # If already using gpt-4o-mini, check if they've exceeded both limits
            if model_code == "gpt-4o-mini":
                # Allow them to continue if they still have message quota
                # This will be checked separately by the message usage service
                pass
            else:
                # If using a premium model, fallback to gpt-4o-mini
                raise PermissionDenied(f"You have exceeded your monthly token limit of {token_limit} tokens. Current usage: {total_used_tokens} tokens. Falling back to gpt-4o-mini.")


class PaidUserTokenUsagePolicy(TokenUsagePolicy):
    """
    Token usage policy for paid users.
    """
    def __init__(self):
        self.daily_token_service = DailyTokenUsagesService()
        self.plan_service = PlanService()
        self.subscription_service = SubscriptionService()

    def check_token_usage(self, user, input_tokens, model_code=None):
        # Get the user's monthly token usage
        total_used_tokens = self.daily_token_service.get_monthly_token_usage(user)

        # Get the user's active subscription
        subscription = self.subscription_service.get(user=user, status='active')

        if subscription:
            # Get the token limit from the user's plan
            token_limit = subscription.plan.tokens_included

            logger.info(f"Checking token usage for paid user {user.id}: used={total_used_tokens}, input={input_tokens}, limit={token_limit}, model={model_code}")

            if total_used_tokens + input_tokens > token_limit:
                # If already using gpt-4o-mini, check if they've exceeded both limits
                if model_code == "gpt-4o-mini":
                    # Allow them to continue if they still have message quota
                    # This will be checked separately by the message usage service
                    pass
                else:
                    # If using a premium model, fallback to gpt-4o-mini
                    raise PermissionDenied(f"You have exceeded your monthly token limit of {token_limit} tokens. Current usage: {total_used_tokens} tokens. Falling back to gpt-4o-mini.")
        else:
            # If no active subscription found, use free plan limits
            free_plan = self.plan_service.get(code="free")
            token_limit = free_plan.tokens_included if free_plan else 50000

            logger.info(f"No active subscription found for user {user.id}, using free plan limit: {token_limit}, model={model_code}")

            if total_used_tokens + input_tokens > token_limit:
                # If already using gpt-4o-mini, check if they've exceeded both limits
                if model_code == "gpt-4o-mini":
                    # Allow them to continue if they still have message quota
                    # This will be checked separately by the message usage service
                    pass
                else:
                    # If using a premium model, fallback to gpt-4o-mini
                    raise PermissionDenied(f"You have exceeded your monthly token limit of {token_limit} tokens. Current usage: {total_used_tokens} tokens. Falling back to gpt-4o-mini.")


class TokenUsagePolicyFactory:
    """
    Factory to provide the appropriate token usage policy based on the user's subscription type.
    """
    @staticmethod
    def get_policy(user):
        if user.subscription_type == "free":
            return FreeUserTokenUsagePolicy()
        else:
            return PaidUserTokenUsagePolicy()