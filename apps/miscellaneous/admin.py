from django.contrib import admin
from config.admin import *
from common.config import BaseAdmin
from apps.miscellaneous.models.site_content import SiteContent

class SiteContentAdmin(BaseAdmin):
    list_display = ['title', 'section', 'company']
    search_fields = ['title', 'section']
    list_filter = ['section', 'company']
    fieldsets = (
        (None, {
            'fields': ('title', 'section', 'content', 'company')
        }),
    )


admin.site.register(SiteContent, SiteContentAdmin)