import os
import json
from twilio.rest import Client
from pywebpush import webpush, WebPushException

from django.conf import settings
from django.utils import timezone
from django.core.mail import send_mail
from fcm_django.models import FCMDevice
from django.template import Template, Context
from django.template.loader import render_to_string

from common.service import BaseService
from apps.notification.repositories.notification_repository import NotificationRepository
from apps.notification.repositories.notification_type_repository import NotificationTypeRepository
from apps.notification.repositories.notification_template_repository import NotificationTemplateRepository
from apps.notification.repositories.notification_preference_repository import NotificationPreferenceRepository
from apps.notification.repositories.email_template_repository import EmailTemplateRepository

from apps.notification.models import (
    EmailTemplate, Notification, NotificationTemplate,
    NotificationType, NotificationPreference
)

from common.logger import Logger
logger = Logger(__name__)

# Firebase Configuration
FCM_SERVER_KEY = os.getenv("FCM_SERVER_KEY")

# VAPID Configuration for Web Push
VAPID_PUBLIC_KEY = os.getenv("VAPID_PUBLIC_KEY")
VAPID_PRIVATE_KEY = os.getenv("VAPID_PRIVATE_KEY")
VAPID_CLAIMS = {
    "sub": "mailto:<EMAIL>"
}


class NotificationService(BaseService):
    def __init__(self):
        self.notification_repository = NotificationRepository()
        self.notification_type_repository = NotificationTypeRepository()
        self.notification_template_repository = NotificationTemplateRepository()
        self.notification_preference_repository = NotificationPreferenceRepository()
        self.email_template_repository = EmailTemplateRepository()

    # ✅ Preferences Handling
    @staticmethod
    def get_preferences(user):
        preference = NotificationPreference.objects.filter(user=user).first()
        if not preference:
            return None
        return {
            "notification_type": [
                nt.code for nt in preference.notification_type.all()
            ],
            "email": preference.email,
            "push": preference.push,
            "sms": preference.sms,
        }

    @staticmethod
    def update_preferences(user, data):
        preference, _ = NotificationPreference.objects.get_or_create(user=user)
        preference.email = data.get("email", preference.email)
        preference.push = data.get("push", preference.push)
        preference.sms = data.get("sms", preference.sms)
        if "notification_type" in data:
            notification_types = NotificationType.objects.filter(
                code__in=data["notification_type"]
            )
            preference.notification_type.set(notification_types)
        preference.save()
        return NotificationService.get_preferences(user)

    # ✅ Notifications Handling
    @staticmethod
    def get_user_notifications(user):
        notifications = Notification.objects.filter(
            user=user
        ).order_by('-created_at')
        if not notifications.exists():
            return []

        return [
            {
                "id": n.id,
                "title": n.title,
                "message": n.message,
                "is_read": n.is_read,
                "created_at": n.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "type": n.type.name if n.type else None,
            }
            for n in notifications
        ]

    @staticmethod
    def create_notification(user, type_code, context=None):
        try:
            notification_type = NotificationType.objects.get(code=type_code)
            template = NotificationTemplate.objects.get(type=notification_type)

            # Render the title and message with context
            title_template = Template(template.subject)
            message_template = Template(template.body)

            title = title_template.render(Context(context or {}))
            message = message_template.render(Context(context or {}))

            # Create the notification
            notification = Notification.objects.create(
                user=user,
                type=notification_type,
                title=title,
                message=message,
            )

            return notification, None
        except NotificationType.DoesNotExist:
            return None, "Invalid notification type"
        except NotificationTemplate.DoesNotExist:
            return None, "Template not found"
        except Exception as e:
            return None, str(e)

    # ✅ Mark Notifications as Read
    @staticmethod
    def mark_as_read(notification_id, user):
        try:
            notification = Notification.objects.get(
                id=notification_id,
                user_id=user.pk  # ✅ Use user_id instead of the User object
            )
            notification.is_read = True
            notification.save()
            return True
        except Notification.DoesNotExist:
            return False

    # ✅ Delete Notifications
    @staticmethod
    def delete_notifications(notification_ids, user):
        try:
            notifications = Notification.objects.filter(
                id__in=notification_ids,
                user_id=user.pk  # ✅ Ensure correct filtering by user_id
            )
            if notifications.exists():
                notifications.delete()
                return True
            return False
        except Exception:
            return False

    # ✅ Preview Email Template
    @staticmethod
    def preview_email_template(template_id, context):
        try:
            template = EmailTemplate.objects.get(id=template_id)
            subject = Template(template.subject).render(Context(context))
            content = Template(template.content).render(Context(context))
            return {
                "subject": subject,
                "content": content,
            }, None
        except EmailTemplate.DoesNotExist:
            return None, "Template not found"

    @staticmethod
    def _send_notification(notification):
        user = notification.user

        # Ensure NotificationPreference exists for the user
        preference, created = NotificationPreference.objects.get_or_create(
            user=user
        )

        # Send Email Notification
        if preference.email:
            NotificationService._send_email_notification(
                user=user,
                title=notification.title,
                message=notification.message,
            )

        # Send Push Notification
        if preference.push:
            NotificationService._send_push_notification(
                user=user,
                title=notification.title,
                message=notification.message,
            )

        # Send SMS Notification
        if preference.sms:
            NotificationService._send_sms(
                phone_number=user.phone_number,
                message=notification.message,
            )

        notification.sent_at = timezone.now()
        notification.save()

    # ✅ Mobile Push Notification Handling
    @staticmethod
    def _send_push_notification(user, title, message):
        devices = FCMDevice.objects.filter(user=user)
        if devices.exists():
            devices.send_message(
                title=title,
                body=message,
                sound="default",
                click_action="FLUTTER_NOTIFICATION_CLICK"
            )

    # ✅ Web Push Notification Handling
    @staticmethod
    def _send_web_push_notification(subscription_info, title, message):
        try:
            webpush(
                subscription_info=subscription_info,
                data=json.dumps({"title": title, "message": message}),
                vapid_private_key=VAPID_PRIVATE_KEY,
                vapid_claims=VAPID_CLAIMS,
            )
        except WebPushException as e:
            logger.error(f"Web Push failed: {str(e)}")

    @staticmethod
    def _send_email_notification(user, title, message):
        if not user.email:
            return False

        context = {
            "title": title,
            "message": message,
            "user": user,
        }

        html_content = render_to_string("email/notification.html", context)
        send_mail(
            subject=title,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
        )

    # ✅ SMS Notification Handling
    @staticmethod
    def _send_sms(phone_number, message):
        account_sid = os.getenv("TWILIO_ACCOUNT_SID")
        auth_token = os.getenv("TWILIO_AUTH_TOKEN")
        twilio_phone_number = os.getenv("TWILIO_PHONE_NUMBER")

        client = Client(account_sid, auth_token)
        sent_message = client.messages.create(
            body=message,
            from_=twilio_phone_number,
            to=phone_number
        )
        return sent_message.sid
