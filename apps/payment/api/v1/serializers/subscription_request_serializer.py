from rest_framework import serializers
from dxh_common.base.base_serializer import BaseModelSerializer
from apps.payment.models.subscription_request import SubscriptionRequest


class SubscriptionRequestSerializer(BaseModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    type_display = serializers.CharField(source='get_type_display', read_only=True)
    
    class Meta:
        model = SubscriptionRequest
        fields = [
            'id', 'user', 'type', 'type_display', 'status', 'status_display', 
            'message', 'submitted_at', 'processed_at'
        ]
        read_only_fields = ['id', 'user', 'status', 'submitted_at', 'processed_at']

class CreateSubscriptionRequestSerializer(BaseModelSerializer):
    type = serializers.ChoiceField(choices=[('downgrade', 'Downgrade'), ('cancel', 'Cancel')])
    message = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    
    class Meta:
        model = SubscriptionRequest
        fields = ['type', 'message']
