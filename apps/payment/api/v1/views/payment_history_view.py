from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services.payment_service import PaymentService

logger = Logger(__name__)



class PaymentHistoryView(BaseApiView):
    def get(self, request):
        payments = PaymentService.get_payment_history(request.user)
        return Response(
            message="Payment history retrieved",
            data=payments
        )
