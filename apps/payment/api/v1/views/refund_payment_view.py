from dxh_libraries.rest_framework import status, Response
from dxh_libraries.translation import gettext_lazy as _
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger

from apps.payment.services.payment_service import PaymentService

logger = Logger(__name__)


class RefundPaymentView(BaseApiView):
    def post(self, request):
        payment_intent_id = request.data.get('payment_intent_id')
        amount = request.data.get('amount')

        refund_id, error = PaymentService.refund_payment(
            payment_intent_id, amount
        )
        if error:
            return Response(message=error)

        return Response(
            message="Refund processed successfully",
            data={'refund_id': refund_id}
        )
