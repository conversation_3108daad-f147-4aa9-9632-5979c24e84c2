import stripe
from django.conf import settings
from dxh_common.logger import Logger

from apps.cross_cutting.email_service import EmailService
from apps.payment.services.plan_service import PlanService
from apps.payment.services.stripe_webhook_log_service import StripeWebhookLogService
from apps.payment.services.subscription_service import SubscriptionService


logger = Logger(__name__)


class StripeWebhookService:
    def __init__(self):
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.plan_service = PlanService()
        self.email_service = EmailService()
        self.subscrption_service = SubscriptionService()
        self.webhook_log_service = StripeWebhookLogService()
        
    def handle_subscription_webhook(self, event):
        event_type = event['type']
        event_id = event['id']
        customer_id = event['data']['object'].get('customer')

        try:
            self.webhook_log_service.create(
                customer_id=customer_id,
                event_id=event_id,
                event_type=event_type,
                event_data=event
            )
        except Exception as e:
            logger.error(f"Error saving webhook log for event {event_id}: {str(e)}")

        webhook_handlers = {
            'invoice.payment_succeeded': self.handle_payment_success,
            'invoice.payment_failed': self.handle_payment_failure,
        }

        handler = webhook_handlers.get(event_type)
        if handler:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"Error processing {event_type}: {str(e)}")
        else:
            logger.info(f"Unhandled Stripe event: {event_type}")

    def handle_payment_success(self, event):
        invoice = event['data']['object']
        stripe_subscription_id = invoice.get('subscription')
        try:
            user_subscription = self.subscrption_service.get(stripe_subscription_id=stripe_subscription_id)
            user_subscription.status = "active"
            user_subscription.save()

            self._send_payment_success_mail(user_subscription)

        except Exception as e:
            logger.error(f"Error handling payment success for subscription {stripe_subscription_id}: {str(e)}")

    def handle_payment_failure(self, event):
        invoice = event['data']['object']
        stripe_subscription_id = invoice.get('subscription')

        try:
            user_subscription = self.subscrption_service.get(stripe_subscription_id=stripe_subscription_id)

            free_plan = self.plan_service.get(code="free")
            user_subscription.plan = free_plan
            user_subscription.status = "past_due"
            user_subscription.save()

            # Notify the user about the failure
            self._send_payment_failure_mail(user_subscription)

        except Exception as e:
            logger.error(f"Error handling payment failure for subscription {stripe_subscription_id}: {str(e)}")

    def _send_payment_success_mail(self, user_subscription):
        user = user_subscription.user
        plan = user_subscription.plan
        next_billing_date = user_subscription.current_period_end.strftime('%Y-%m-%d') if user_subscription.current_period_end else "N/A"
        context = {
            "user": user,
            "plan": plan,
            "next_billing_date": next_billing_date,
        }

        self.email_service.send_email(
            to_email=user_subscription.user.email,
            subject="Your Subscription Payment Was Successful",
            template_name="subscription_success",
            context=context,
        )

    def _send_payment_failure_mail(self, user_subscription):
        user = user_subscription.user
        context = {
            "user": user,
            "plan": user_subscription.plan,
        }

        self.email_service.send_email(
            to_email=user.email,
            subject="Your Subscription Payment Failed",
            template_name="payment_failure",
            context=context,
        )