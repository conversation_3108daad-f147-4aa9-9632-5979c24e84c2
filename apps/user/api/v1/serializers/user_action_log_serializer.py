from apps.user.models.user_action_log import UserActionLog
from rest_framework import serializers



class UserActionLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserActionLog
        fields = ['user', 'message_id', 'conversation_id', 'source', 'action_type', 'selected_text', 'location','metadata']

    def validate(self, data):
        """
        Custom validation if needed. For example, ensure message_id and conversation_id are present.
        """
        if not data.get("message_id"):
            raise serializers.ValidationError("message_id is required.")
        if not data.get("conversation_id"):
            raise serializers.ValidationError("conversation_id is required.")
        return data
