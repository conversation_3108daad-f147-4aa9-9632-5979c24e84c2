from django.conf import settings
from django.contrib.auth import get_user_model

from easy_thumbnails.files import get_thumbnailer
from rest_framework import serializers

from apps.user.api.v1.serializers.user_preference_serializers import UserPreferenceSerializer
from apps.user.models.user_preference import UserPreference
from common.serializers import BaseModelSerializer

User = get_user_model()


class UserSerializer(BaseModelSerializer):
    is_password_set = serializers.SerializerMethodField()
    profile_picture_url = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id', 'company_id', 'email', 'username', 'name', 'first_name', 'last_name', 'phone', 
            'profile_picture_url', 'is_email_verified', 'is_phone_verified', 'mfa_enabled', 'is_password_set', 'subscription_type'
        )
        read_only_fields = (
            'id', 'company_id', 'is_email_verified', 'is_phone_verified', 'mfa_enabled', 'is_password_set'
        )

    def get_is_password_set(self, obj):
        return obj.has_usable_password()
    
    def get_profile_picture_url(self, obj):
        if obj.profile_picture:
            thumbnail = get_thumbnailer(obj.profile_picture)['profile']
            return settings.MEDIA_BASE_URL +thumbnail.url
        return None
    

class UserDetailSerializer(BaseModelSerializer):
    preferences = UserPreferenceSerializer(source='userpreference', read_only=True)
    is_password_set = serializers.SerializerMethodField()
    profile_picture_url = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id', 'email', 'username', 'name', 'first_name', 'last_name', 'phone', 'profile_picture_url', 
            'is_active', 'is_email_verified', 'is_phone_verified',
            'mfa_enabled', 'preferences', 'last_login', 'date_joined', 'is_password_set', 'subscription_type'
        )
        read_only_fields = (
            'id', 'email', 'is_email_verified', 'is_phone_verified',
            'mfa_enabled', 'last_login', 'date_joined', 'is_password_set'
        )

    def get_profile_picture_url(self, obj):
        if obj.profile_picture:
            thumbnail = get_thumbnailer(obj.profile_picture)['profile']
            return settings.MEDIA_BASE_URL +thumbnail.url
        return None
    
    def get_is_password_set(self, obj):
        return obj.has_usable_password()


class UserListSerializer(BaseModelSerializer):
    preferences = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id', 'email', 'username', 'first_name', 'last_name',
            'is_active', 'preferences', 'created_at'
        )
        read_only_fields = ('id', 'created_at')

    def get_preferences(self, obj):
        try:
            return {
                'language': obj.userpreference.language,
                'timezone': obj.userpreference.timezone
            }
        except UserPreference.DoesNotExist:
            return None