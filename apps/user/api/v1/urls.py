from django.urls import path
from apps.user.api.v1.views import (
    account_views,
    user_views,
    group_views,
    preferences_views,
    connect_app_view,
    user_action_log_view,
)

urlpatterns = [
    # Users
    path('users/', user_views.UserListView.as_view(), name='user-list'),
    path('users/<int:user_id>/', user_views.UserDetailView.as_view(), name='user-detail'),
    path('users/<int:user_id>/status/', user_views.UserStatusView.as_view(), name='user-status'),
    path('users/search/', user_views.UserSearchView.as_view(), name='user-search'),
    path('users/filters/', user_views.UserFilterView.as_view(), name='user-filter'),
    path('users/update-profile/', user_views.UserProfileView.as_view(), name='update-profile'),

    # Account
    path('accounts/deletion/request/', account_views.RequestDeletionView.as_view(), name='request-deletion'),
    path('accounts/deletion/cancel/', account_views.CancelDeletionView.as_view(), name='cancel-deletion'),
    path('accounts/<int:user_id>/export/', account_views.ExportDataView.as_view(), name='export-data'),

    # Preferences
    path('preferences/', preferences_views.UserPreferenceView.as_view(), name='user-preferences'),
    path('preferences/theme/', preferences_views.ThemePreferenceView.as_view(), name='theme-preference'),
    path('preferences/language/', preferences_views.LanguagePreferenceView.as_view(), name='language-preference'),
    path('preferences/timezone/', preferences_views.TimezonePreferenceView.as_view(), name='timezone-preference'),

    # Groups and Permissions
    path('groups/', group_views.GroupListView.as_view(), name='group-list'),
    path('groups/<int:group_id>/', group_views.GroupDetailView.as_view(), name='group-detail'),
    path('groups/<int:group_id>/permissions/', group_views.GroupPermissionAPIView.as_view(), name='group-permission-list-add'),
    path('groups/<int:group_id>/users/', group_views.GroupUserView.as_view(), name='group-users'),
    path('permissions/', group_views.PermissionListView.as_view(), name='permission-list'),
    path('permissions/<int:permission_id>/', group_views.PermissionDetailView.as_view(), name='permission-detail'),

    # Connected Apps
    path('connect-apps/', connect_app_view.ConnectAppAPIView.as_view(), name='connected-app'),
    
    path('implicit-feedback/', user_action_log_view.UserActionLogView.as_view(), name='implicit-feedback'),
]
