from typing import Any
from apps.identity.utils.user_login_activity import log_activity
from dxh_libraries.rest_framework import status, Response
from dxh_common.permissions import IsAuthenticated

from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from apps.user.models.user_preference import UserPreference
from apps.user.services.user_preference_service import UserPreferenceService

logger = Logger(__name__)


class LanguagePreferenceView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_preference_service = UserPreferenceService()
    
    def get(self, request):
        """Get current language preference"""
        preferences = self.user_preference_service.get_user_preferences(
            request.user
        )

        result = {
            "message": "Language preference retrieved",
            "data": preferences['language']
        }
        return Response(result)

    def put(self, request):
        language = request.data.get('language')

        if not language:
            result = {
                "message": "Language is required"
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.user_preference_service.update_language_preference(
                request.user, language
            )
            log_activity(
                request.user, 
                'language_change', 
                f"Changed language to {request.data.get('language')}", 
                request
            )
            result = {
                "message": "Language preference updated",
                "data": language
            }
            return Response(result)

        except Exception as e:
            result = {"message": str(e)}
            return Response(
                result, status=status.HTTP_400_BAD_REQUEST
            )


class TimezonePreferenceView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_preference_service = UserPreferenceService()

    def put(self, request):
        timezone = request.data.get('timezone')

        if not timezone:
            result = {"message": "Timezone is required"}
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.user_preference_service.update_timezone_preference(
                request.user, timezone
            )
            result = {"message": "Timezone preference updated"}
            return Response(result)

        except Exception as e:
            result = {"message": str(e)}
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )


class UserPreferenceView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_preference_service = UserPreferenceService()

    def get(self, request):
        try:
            preferences = self.user_preference_service.get_user_preferences(
                request.user
            )
            result = {
                "message": "Preferences retrieved successfully",
                "data": preferences
            }
            return Response(result)

        except UserPreference.DoesNotExist:
            result = {
                "message": "User preferences not found"
            }
            return Response(
                result,
                status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request):
        language = request.data.get('language')

        if not language:
            result = {
                "message": "Language is required"
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.user_preference_service.update_language_preference(
                request.user, language
            )
            result = {
                "message": "Language preference updated"
            }
            return Response(result)

        except Exception as e:
            result = {"message": str(e)}
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )
        
class ThemePreferenceView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_preference_service = UserPreferenceService()
    
    def get(self, request):
        """Get current theme preference"""
        preferences = self.user_preference_service.get_user_preferences(
            request.user
        )

        result = {
            "message": "Theme preference retrieved",
            "data": preferences['theme']
        }
        return Response(result)
    
    def put(self, request):
        theme_preference = request.data.get('theme_preference')

        if not theme_preference:
            result = {
                "message": "Theme preference is required"
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.user_preference_service.update_theme_preference(
                request.user, theme_preference
            )
            log_activity(
                request.user, 
                'theme_change', 
                f"Changed theme preference to {request.data.get('ththeme_preferencee')}", 
                request
            )
            result = {
                "message": "Theme preference updated",
                "data": theme_preference
            }
            return Response(result)

        except Exception as e:
            raise e
