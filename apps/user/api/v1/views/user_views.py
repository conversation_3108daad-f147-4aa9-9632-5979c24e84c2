from typing import Any
from dxh_libraries.rest_framework import status, Response
from rest_framework.permissions import IsAdminUser
from dxh_libraries.rest_framework import pagination
# Using pagination.PageNumberPagination
from rest_framework.generics import ListAPIView

from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django_filters import rest_framework as filters

from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from common.paginations import CustomPagination
from apps.user.services.user_service import UserService
from apps.user.api.v1.serializers.user_serializers import (
    UserListSerializer,
    UserDetailSerializer,
)

User = get_user_model()
logger = Logger(__name__)


class UserListView(BaseApiView):
    permission_classes = [IsAdminUser]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.paginator = CustomPagination()

    def get(self, request):
        users = self.user_service.get_list_users()
        paginated_data = self.paginator.paginate_queryset(users, request)
        serializer = UserListSerializer(paginated_data, many=True)
        paginated_response = self.paginator.get_paginated_response(serializer.data)
        result = {
            "message": "Users retrieved successfully",
            "data": paginated_response.data
        }
        return Response(result)

    def post(self, request):
        serializer = UserDetailSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            result = {
                "message": "User created successfully",
                "data": UserDetailSerializer(user).data,
            }
            return Response(
                result,
                status=status.HTTP_201_CREATED
            )
        result = {
            "message": "Validation error",
            "errors": serializer.errors
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)


class UserDetailView(BaseApiView):
    permission_classes = [IsAdminUser]

    def get(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        serializer = UserDetailSerializer(user)
        result = {
            "message": "User details retrieved",
            "data": serializer.data
        }
        return Response(result)

    def put(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        serializer = UserDetailSerializer(
            user, data=request.data, partial=True
        )
        if serializer.is_valid():
            user = serializer.save()
            result = {
                "message": "User updated successfully",
                "data": serializer.data
            }
            return Response(result)

        result = {
            "message": "Validation error",
            "errors": serializer.errors
        }
        return Response(result)

    def delete(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        user.is_active = False
        user.save()
        result = {
            "message": "User deactivated successfully"
        }
        return Response(result)


# Search and Filter
class UserSearchView(BaseApiView):
    permission_classes = [IsAdminUser]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    def get(self, request):
        try:
            query = request.query_params.get('q', '')
            users = self.user_service.search_users(query)
            result = {
                "message": "Search results retrieved",
                "data": UserListSerializer(users, many=True).data
            }
            return Response(result)
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            result = {
                "message": "Error retrieving search results",
            }
            return Response(
                result, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserFilter(filters.FilterSet):
    status = filters.BooleanFilter(field_name='is_active')
    role = filters.NumberFilter(field_name='roles__id')
    email_verified = filters.BooleanFilter(field_name='is_email_verified')
    phone_verified = filters.BooleanFilter(field_name='is_phone_verified')
    date_joined_after = filters.DateFilter(
        field_name='date_joined', lookup_expr='gte'
    )
    date_joined_before = filters.DateFilter(
        field_name='date_joined', lookup_expr='lte'
    )

    class Meta:
        model = User
        fields = ['status', 'role', 'email_verified', 'phone_verified']


class UserFilterView(ListAPIView):
    permission_classes = [IsAdminUser]
    queryset = User.objects.all()
    serializer_class = UserListSerializer
    filter_backends = [filters.DjangoFilterBackend]
    filterset_class = UserFilter
    pagination_class = CustomPagination

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                # return self.get_paginated_response(serializer.data)
                data = self.get_paginated_response(serializer.data).data
                return Response(
                    {
                        "message": "Filter results retrieved",
                        "data": data
                    },
                    status=status.HTTP_200_OK
                )

            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {"message": "Filter results retrieved", "data": serializer.data},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Filter error: {str(e)}")
            return Response(
                {"message": "Error retrieving filter results"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

# User Profile Views
class UserStatusView(BaseApiView):
    permission_classes = [IsAdminUser]

    def put(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        status = request.data.get('status')

        if status not in ['active', 'inactive', 'banned']:
            result = {
                "message": "Invalid status"
            }
            return Response(
                result,
                status=status.HTTP_400_BAD_REQUEST
            )

        user.status = status
        user.save()

        result = {
            "message": f"User status updated to {status}"
        }
        return Response(result)


class UserProfileView(BaseApiView):

    def put(self, request):
        try:
            user = request.user
            serializer = UserDetailSerializer(
                user, data=request.data, partial=True
            )

            if not serializer.is_valid():
                result = {
                    "message": "Validation error",
                    "errors": serializer.errors,
                }
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

            serializer.save()

            result = {
                "message": "Profile updated successfully",
                "data": serializer.data,
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error updating profile: {str(e)}")
            raise e
