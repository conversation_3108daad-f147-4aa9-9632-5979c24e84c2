from common.repository import BaseRepository
from apps.user.models.daily_message_usage import DailyMessageUsage
from django.utils import timezone


class DailyMessageUsageRepository(BaseRepository):
    def __init__(self):
        super().__init__(DailyMessageUsage)

    def get_daily_message_usage(self, user):
        today = timezone.now().date()
        
        try:
            usage = self.model.objects.get(user=user, activity_date=today)

            return usage
        
        except self.model.DoesNotExist:
            return None
