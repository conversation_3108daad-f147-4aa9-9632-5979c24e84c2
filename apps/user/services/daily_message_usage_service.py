from common.service import BaseService, ServiceError
from apps.user.repositories.daily_message_usage_repository import DailyMessageUsageRepository
from datetime import date
from apps.payment.services.subscription_service import SubscriptionService
from apps.payment.services.plan_service import PlanService
from dxh_common.logger import Logger

logger = Logger(__name__)


class DailyMessageUsageService(BaseService):
    def __init__(self):
        super().__init__(DailyMessageUsageRepository())
        self.subscription_service = SubscriptionService()
        self.plan_service = PlanService()
    
    def update_daily_file_upload_count(self, user):
        try:
            obj, created = self.repository.get_or_create(user=user, activity_date=date.today())
            obj.file_upload_count += 1
            obj.save()

            return obj

        except Exception as e:
            logger.error(f"Error updating daily file upload usage for user {user.id}: {e}")
            raise ServiceError(f"Error updating daily file upload usage: {e}")
        
    def _get_user_daily_file_upload_limit(self, user):
        try:
            subscription = self.subscription_service.get(user=user, status='active')

            if subscription and subscription.daily_file_upload_limit > 0:
                return subscription.daily_file_upload_limit

            free_plan = self.plan_service.get(code="free")
            if free_plan:
                return free_plan.daily_file_upload_limit

            return 0
        
        except Exception as e:
            logger.error(f"Error getting daily file upload limit for user {user.id}: {e}")
            raise ServiceError(f"Error getting daily file upload limit: {e}")

    def update_daily_usage(self, user):
        try:
            obj, created = self.repository.get_or_create(user=user, activity_date=date.today())

            # Get the daily limit for comparison
            daily_limit = self._get_user_daily_limit(user)

            # Increment message count
            obj.message_count += 1
            obj.save()

            # Log the update
            if created:
                logger.info(f"Created new daily message usage for user {user.id}: count=1, limit={daily_limit}")
            else:
                logger.info(f"Updated daily message usage for user {user.id}: count={obj.message_count}, limit={daily_limit}")

                # Check if user has exceeded their limit
                if obj.message_count > daily_limit:
                    logger.warning(f"User {user.id} has exceeded their daily message limit: {obj.message_count}/{daily_limit}")

            return obj

        except Exception as e:
            logger.error(f"Error updating daily message usage for user {user.id}: {e}")
            raise ServiceError(f"Error updating daily message usage: {e}")

    def check_daily_limit(self, user):
        try:
            usage = self.repository.get_daily_message_usage(user)
            daily_limit = self._get_user_daily_limit(user)

            logger.info(f"Checking daily message limit for user {user.id}: subscription_type={user.subscription_type}")

            if not usage:
                logger.info(f"No usage found for user {user.id}, daily_limit={daily_limit}")
                return True, 0, daily_limit

            remaining = daily_limit - usage.message_count

            logger.info(f"User {user.id} message usage: count={usage.message_count}, limit={daily_limit}, remaining={remaining}")

            return remaining > 0, usage.message_count, daily_limit

        except Exception as e:
            logger.error(f"Error checking daily message limit for user {user.id}: {e}")
            raise ServiceError(f"Error checking daily message limit: {e}")

    def _get_user_daily_limit(self, user):
        try:
            # Get active subscription
            subscription = self.subscription_service.get(user=user, status='active')

            if subscription and subscription.daily_message_limit > 0:
                logger.info(f"User {user.id} has active subscription with daily_message_limit={subscription.daily_message_limit}, plan={subscription.plan.code}")
                return subscription.daily_message_limit

            # If no active subscription or limit is 0, use the free plan limit
            free_plan = self.plan_service.get(code="free")
            if free_plan:
                logger.info(f"User {user.id} using free plan limit: {free_plan.daily_message_limit}")
                return free_plan.daily_message_limit

            # Fallback to default limit
            logger.info(f"User {user.id} using default limit: 10")
            return 10

        except Exception as e:
            logger.error(f"Error getting daily message limit for user {user.id}: {e}")
            raise ServiceError(f"Error getting daily message limit: {e}")

    def get_daily_file_useage(self, user):
        try:
            usage = self.get(user=user, activity_date=date.today())
            if not usage:
                return 0

            return usage.file_upload_count
        
        except Exception as e:
            logger.error(f"Error getting daily file useage for user {user.id}: {e}")
            raise ServiceError(f"Error getting daily file useage: {e}")

    def update_daily_image_generation_count(self, user):
        try:
            obj, created = self.repository.get_or_create(user=user, activity_date=date.today())
            obj.image_generation_count += 1
            obj.save()

            return obj

        except Exception as e:
            logger.error(f"Error updating daily file upload usage for user {user.id}: {e}")
            raise ServiceError(f"Error updating daily file upload usage: {e}")
    
    def get_user_daily_image_generation_limit(self, user):
        try:
            subscription = self.subscription_service.get(user=user, status='active')

            if subscription and subscription.daily_image_generation_limit > 0:
                return subscription.daily_image_generation_limit

            free_plan = self.plan_service.get(code="free")
            if free_plan:
                return free_plan.daily_image_generation_limit

            return 0
        
        except Exception as e:
            logger.error(f"Error getting daily file upload limit for user {user.id}: {e}")
            raise ServiceError(f"Error getting daily file upload limit: {e}")
        
    def check_daily_image_generation_limit(self, user):
        try:
            usage = self.repository.get_daily_message_usage(user)
            daily_limit = self.get_user_daily_image_generation_limit(user)
            if not usage:
                return True, 0, daily_limit

            remaining = daily_limit - usage.image_generation_count

            return remaining > 0, usage.image_generation_count, daily_limit

        except Exception as e:
            logger.error(f"Error checking daily image generation limit for user {user.id}: {e}")
            raise ServiceError(f"Error checking daily image generation limit: {e}")
