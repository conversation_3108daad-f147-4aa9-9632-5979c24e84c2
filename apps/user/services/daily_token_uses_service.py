from common.service import BaseService, ServiceError
from apps.user.repositories.daily_token_usages_repository import DailyTokenUsagesRepository
from datetime import date
from dxh_common.logger import Logger

logger = Logger(__name__)

class DailyTokenUsagesService(BaseService):
    def __init__(self):
        super().__init__(DailyTokenUsagesRepository())

    def update_daily_usages(self, user, input_tokens, output_tokens):
        try:
            obj, _ = self.repository.get_or_create(user=user, activity_date=date.today())
            obj.prompt_tokens += input_tokens
            obj.completion_tokens += output_tokens
            obj.save()

            logger.info(f"Updated token usage for user {user.id}: +{input_tokens} input, +{output_tokens} output, total: {obj.total_tokens}")
            return obj.total_tokens
        except Exception as e:
            logger.error(f"Error updating token usage for user {user.id}: {e}")
            raise ServiceError(f"Error updating token usage: {e}")

    def get_monthly_token_usage(self, user):
        try:
            monthly_token_usage = self.repository.get_monthly_token_usage(user)
            logger.info(f"Monthly token usage for user {user.id}: {monthly_token_usage}")
            return monthly_token_usage

        except Exception as e:
            logger.error(f"Error getting monthly token usage for user {user.id}: {e}")
            raise ServiceError(f"Service error during get operation: {e}")