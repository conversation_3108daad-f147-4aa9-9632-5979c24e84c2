from django.contrib.auth import get_user_model
from common.logger import Logger
from common.constants import Language, Theme
from common.service import BaseService, ServiceError
from apps.user.repositories.users_preference_repository import UserPreferenceRepository

User = get_user_model()
logger = Logger(__name__)


class UserPreferenceService(BaseService):
    def __init__(self):
        super().__init__(UserPreferenceRepository())

    def create_user_preference(self, data):
        try:
            # check user preference already exists
            user_preference = self.repository.get(user=data['user'])
            if not user_preference:
                user_preference = self.repository.create(**data) 

            return user_preference
        
        except Exception as e:
            logger.error(f"Error creating user with email {data['language']}: {str(e)}")
            raise ServiceError(f"Error creating user: {str(e)}")


    def get_user_preference(self, user):
        try:
            user_preference = self.repository.get(user=user)
            logger.info(f"User preference retrieved for user {user.pk}: {user_preference}")
            
            return user_preference
        
        except Exception as e:
            logger.error(f"Failed to retrieve user preference for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error retrieving user preference: {str(e)}")

    def get_or_create_user_preference(self, user):
        try:
            user_preference, created = self.repository.get_or_create(user=user)
            logger.info(f"User preference retrieved for user {user.pk}: {user_preference}")

            return user_preference, created

        except Exception as e:
            logger.error(f"Failed to retrieve user preference for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error retrieving user preference: {str(e)}")

    def update_language_preference(self, user, language):
        try:
            user_preference = self.repository.get(user=user)
            if not user_preference:
                # Use self.create to create a new user preference
                user_preference = self.create({"user": user, "language": language})
            else:
                user_preference.language = language
                user_preference.save()
            
            logger.info(f"Language preference updated for user {user.pk} to {language}.")
            return user_preference
        
        except Exception as e:
            logger.error(f"Failed to update language preference for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error updating language preference: {str(e)}")

    def update_timezone_preference(self, user, timezone):
        try:
            user_preference = self.repository.get(user=user)
            user_preference.timezone = timezone
            user_preference.save()
            logger.info(f"Timezone preference updated for user {user.pk} to {timezone}.")
            
            return user_preference
        
        except Exception as e:
            logger.error(f"Failed to update timezone preference for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error updating timezone preference: {str(e)}")
        
    def update_theme_preference(self, user, theme_preference):
        try:
            user_preference = self.repository.get(user=user)
            user_preference.theme = theme_preference
            user_preference.save()
            logger.info(f"Theme preference updated for user {user.pk} to {theme_preference}.")
            
            return user_preference
        
        except Exception as e:
            logger.error(f"Failed to update theme preference for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error updating theme preference: {str(e)}")


    def get_user_preferences(self, user):
        try:
            preferences = self.repository.get(user=user)
            result = {
                "language": preferences.language if preferences else Language.ENGLISH.value,
                "timezone": preferences.timezone if preferences else "UTC",
                "theme": preferences.theme if preferences else Theme.LIGHT.value,
            }
            logger.info(f"Preferences retrieved for user {user.pk}: {result}")
            
            return result
        
        except Exception as e:
            logger.error(f"Failed to retrieve preferences for user {user.pk}: {str(e)}")
            raise ServiceError(f"Error retrieving preferences: {str(e)}")
